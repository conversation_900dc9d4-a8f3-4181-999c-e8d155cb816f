variables:
  BuildDir: ./build

.go-cache:
  variables:
    GOMODCACHE: $CI_PROJECT_DIR/.gomodcache
  before_script:
    - export GOPROXY='https://goproxy.cn|https://goproxy.io,direct'
    - export GOSUMDB=off
    - export GOPRIVATE=git.kicad99.com
    - mkdir -p $GOMODCACHE
  cache:
    key:
      files:
        - go.sum
      prefix: ${CI_COMMIT_REF_SLUG}
    paths:
      - $GOMODCACHE

stages:
  - lint
  - build-linux64
  - build-win64

lint:
  stage: lint
  image: ghcr.io/yangjuncode/go:1.24.3
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
  script:
    - go vet ./...

build-linux64:
  stage: build-linux64
  image: ghcr.io/yangjuncode/go:1.24.3
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.txt
  script:
    - ./build.sh
  artifacts:
    expire_in: 7 day
    name: sip_server_client_linux_x64
    paths:
      - $BuildDir

build-win64:
  stage: build-win64
  image: ghcr.io/yangjuncode/go:1.24.3
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.txt
  script:
    - ./build_win.sh
  artifacts:
    expire_in: 7 day
    name: sip_server_client_win_x64
    paths:
      - $BuildDir
