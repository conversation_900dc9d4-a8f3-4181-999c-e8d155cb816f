{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Flutter Run linux client",
        "type": "dart",
        "request": "launch",
        "program": "${workspaceFolder}/flutter_client/lib/main.dart"
      },
      {
        "name": "Launch go client",
        "type": "go",
        "request": "launch",
        "mode": "auto",
        "program": "${workspaceFolder}/cmd/sipserverclient",
        "cwd": "${workspaceFolder}/build",
        "args": [
          "-s",
          "t2.bfdx.net",
          "-p",
          "2252",
          "-u",
          "540000",
          "-P",
          "1234",
          "-transport",
          "udp",
          "-dmr-target",
          "0x80088217",
          "-sound",
          "test.mp3"
        ]
      }
    ]
}