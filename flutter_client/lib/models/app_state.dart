import 'package:flutter/material.dart';

/// 应用状态模型
class AppState {
  final bool isDarkMode;
  final Locale locale;
  final bool isNotificationEnabled;
  final String serverUrl;
  final int connectionTimeout;

  const AppState({
    this.isDarkMode = false,
    this.locale = const Locale('zh', 'CN'),
    this.isNotificationEnabled = true,
    this.serverUrl = 'http://localhost:8080',
    this.connectionTimeout = 30000,
  });

  AppState copyWith({
    bool? isDarkMode,
    Locale? locale,
    bool? isNotificationEnabled,
    String? serverUrl,
    int? connectionTimeout,
  }) {
    return AppState(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      locale: locale ?? this.locale,
      isNotificationEnabled: isNotificationEnabled ?? this.isNotificationEnabled,
      serverUrl: serverUrl ?? this.serverUrl,
      connectionTimeout: connectionTimeout ?? this.connectionTimeout,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppState &&
        other.isDarkMode == isDarkMode &&
        other.locale == locale &&
        other.isNotificationEnabled == isNotificationEnabled &&
        other.serverUrl == serverUrl &&
        other.connectionTimeout == connectionTimeout;
  }

  @override
  int get hashCode {
    return isDarkMode.hashCode ^
        locale.hashCode ^
        isNotificationEnabled.hashCode ^
        serverUrl.hashCode ^
        connectionTimeout.hashCode;
  }

  @override
  String toString() {
    return 'AppState(isDarkMode: $isDarkMode, locale: $locale, isNotificationEnabled: $isNotificationEnabled, serverUrl: $serverUrl, connectionTimeout: $connectionTimeout)';
  }
}

/// 用户状态模型
class UserState {
  final bool isLoggedIn;
  final String? username;
  final String? token;
  final Map<String, dynamic>? userInfo;

  const UserState({
    this.isLoggedIn = false,
    this.username,
    this.token,
    this.userInfo,
  });

  UserState copyWith({
    bool? isLoggedIn,
    String? username,
    String? token,
    Map<String, dynamic>? userInfo,
  }) {
    return UserState(
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      username: username ?? this.username,
      token: token ?? this.token,
      userInfo: userInfo ?? this.userInfo,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserState &&
        other.isLoggedIn == isLoggedIn &&
        other.username == username &&
        other.token == token &&
        other.userInfo == userInfo;
  }

  @override
  int get hashCode {
    return isLoggedIn.hashCode ^
        username.hashCode ^
        token.hashCode ^
        userInfo.hashCode;
  }

  @override
  String toString() {
    return 'UserState(isLoggedIn: $isLoggedIn, username: $username, token: $token, userInfo: $userInfo)';
  }
}
