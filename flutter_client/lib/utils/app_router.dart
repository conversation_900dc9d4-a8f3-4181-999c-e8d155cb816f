import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_constants.dart';
import '../pages/home_page.dart';
import '../pages/login_page.dart';
import '../pages/settings_page.dart';

/// 应用路由配置
class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: AppConstants.homePath,
    routes: [
      // 主页路由
      GoRoute(
        path: AppConstants.homePath,
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),
      
      // 登录页路由
      GoRoute(
        path: AppConstants.loginPath,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      
      // 设置页路由
      GoRoute(
        path: AppConstants.settingsPath,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
      ),
    ],
    
    // 错误页面处理
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('页面未找到'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              '页面未找到',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              '请求的页面不存在: ${state.uri}',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppConstants.homePath),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    ),
    
    // 重定向逻辑
    redirect: (context, state) {
      // TODO: 根据用户登录状态进行重定向
      // 例如：如果用户未登录且不在登录页面，则重定向到登录页面
      return null;
    },
  );

  static GoRouter get router => _router;
}

/// 路由扩展方法
extension AppRouterExtension on BuildContext {
  /// 导航到主页
  void goHome() => go(AppConstants.homePath);
  
  /// 导航到登录页
  void goLogin() => go(AppConstants.loginPath);
  
  /// 导航到设置页
  void goSettings() => go(AppConstants.settingsPath);
  
  /// 返回上一页
  void goBack() => pop();
}
