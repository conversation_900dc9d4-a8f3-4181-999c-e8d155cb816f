import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/app_state.dart';
import '../services/storage_service.dart';

/// 应用状态提供者
class AppStateNotifier extends StateNotifier<AppState> {
  final StorageService _storageService;

  AppStateNotifier(this._storageService) : super(const AppState()) {
    _loadSettings();
  }

  /// 加载设置
  Future<void> _loadSettings() async {
    try {
      final settings = await _storageService.getAppSettings();
      if (settings != null) {
        state = AppState(
          isDarkMode: settings['isDarkMode'] ?? false,
          locale: Locale(
            settings['languageCode'] ?? 'zh',
            settings['countryCode'] ?? 'CN',
          ),
          isNotificationEnabled: settings['isNotificationEnabled'] ?? true,
          serverUrl: settings['serverUrl'] ?? 'http://localhost:8080',
          connectionTimeout: settings['connectionTimeout'] ?? 30000,
        );
      }
    } catch (e) {
      debugPrint('加载设置失败: $e');
    }
  }

  /// 切换主题模式
  Future<void> toggleThemeMode() async {
    final newState = state.copyWith(isDarkMode: !state.isDarkMode);
    state = newState;
    await _saveSettings();
  }

  /// 设置语言
  Future<void> setLocale(Locale locale) async {
    final newState = state.copyWith(locale: locale);
    state = newState;
    await _saveSettings();
  }

  /// 切换通知设置
  Future<void> toggleNotification() async {
    final newState = state.copyWith(
      isNotificationEnabled: !state.isNotificationEnabled,
    );
    state = newState;
    await _saveSettings();
  }

  /// 设置服务器地址
  Future<void> setServerUrl(String url) async {
    final newState = state.copyWith(serverUrl: url);
    state = newState;
    await _saveSettings();
  }

  /// 设置连接超时
  Future<void> setConnectionTimeout(int timeout) async {
    final newState = state.copyWith(connectionTimeout: timeout);
    state = newState;
    await _saveSettings();
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    try {
      await _storageService.saveAppSettings({
        'isDarkMode': state.isDarkMode,
        'languageCode': state.locale.languageCode,
        'countryCode': state.locale.countryCode,
        'isNotificationEnabled': state.isNotificationEnabled,
        'serverUrl': state.serverUrl,
        'connectionTimeout': state.connectionTimeout,
      });
    } catch (e) {
      debugPrint('保存设置失败: $e');
    }
  }
}

/// 用户状态提供者
class UserStateNotifier extends StateNotifier<UserState> {
  final StorageService _storageService;

  UserStateNotifier(this._storageService) : super(const UserState()) {
    _loadUserInfo();
  }

  /// 加载用户信息
  Future<void> _loadUserInfo() async {
    try {
      final token = await _storageService.getUserToken();
      final userInfo = await _storageService.getUserInfo();
      
      if (token != null && userInfo != null) {
        state = UserState(
          isLoggedIn: true,
          username: userInfo['username'],
          token: token,
          userInfo: userInfo,
        );
      }
    } catch (e) {
      debugPrint('加载用户信息失败: $e');
    }
  }

  /// 用户登录
  Future<void> login(String username, String token, Map<String, dynamic> userInfo) async {
    try {
      await _storageService.saveUserToken(token);
      await _storageService.saveUserInfo(userInfo);
      
      state = UserState(
        isLoggedIn: true,
        username: username,
        token: token,
        userInfo: userInfo,
      );
    } catch (e) {
      debugPrint('保存用户信息失败: $e');
      rethrow;
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      await _storageService.clearUserToken();
      await _storageService.clearUserInfo();
      
      state = const UserState();
    } catch (e) {
      debugPrint('清除用户信息失败: $e');
    }
  }
}

/// 存储服务提供者
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

/// 应用状态提供者
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return AppStateNotifier(storageService);
});

/// 用户状态提供者
final userStateProvider = StateNotifierProvider<UserStateNotifier, UserState>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return UserStateNotifier(storageService);
});

/// 主题模式提供者
final themeModeProvider = Provider<ThemeMode>((ref) {
  final appState = ref.watch(appStateProvider);
  return appState.isDarkMode ? ThemeMode.dark : ThemeMode.light;
});

/// 当前语言提供者
final localeProvider = Provider<Locale>((ref) {
  final appState = ref.watch(appStateProvider);
  return appState.locale;
});
