import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';

/// 存储服务
/// 注意：这是一个简化的实现，实际项目中建议使用 shared_preferences 或其他持久化存储方案
class StorageService {
  // 内存存储（仅用于演示，实际应用中应使用持久化存储）
  static final Map<String, String> _storage = {};

  /// 保存用户令牌
  Future<void> saveUserToken(String token) async {
    try {
      _storage[AppConstants.userTokenKey] = token;
      debugPrint('用户令牌已保存');
    } catch (e) {
      debugPrint('保存用户令牌失败: $e');
      rethrow;
    }
  }

  /// 获取用户令牌
  Future<String?> getUserToken() async {
    try {
      return _storage[AppConstants.userTokenKey];
    } catch (e) {
      debugPrint('获取用户令牌失败: $e');
      return null;
    }
  }

  /// 清除用户令牌
  Future<void> clearUserToken() async {
    try {
      _storage.remove(AppConstants.userTokenKey);
      debugPrint('用户令牌已清除');
    } catch (e) {
      debugPrint('清除用户令牌失败: $e');
    }
  }

  /// 保存用户信息
  Future<void> saveUserInfo(Map<String, dynamic> userInfo) async {
    try {
      _storage[AppConstants.userInfoKey] = jsonEncode(userInfo);
      debugPrint('用户信息已保存');
    } catch (e) {
      debugPrint('保存用户信息失败: $e');
      rethrow;
    }
  }

  /// 获取用户信息
  Future<Map<String, dynamic>?> getUserInfo() async {
    try {
      final userInfoStr = _storage[AppConstants.userInfoKey];
      if (userInfoStr != null) {
        return jsonDecode(userInfoStr) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('获取用户信息失败: $e');
      return null;
    }
  }

  /// 清除用户信息
  Future<void> clearUserInfo() async {
    try {
      _storage.remove(AppConstants.userInfoKey);
      debugPrint('用户信息已清除');
    } catch (e) {
      debugPrint('清除用户信息失败: $e');
    }
  }

  /// 保存应用设置
  Future<void> saveAppSettings(Map<String, dynamic> settings) async {
    try {
      _storage[AppConstants.settingsKey] = jsonEncode(settings);
      debugPrint('应用设置已保存');
    } catch (e) {
      debugPrint('保存应用设置失败: $e');
      rethrow;
    }
  }

  /// 获取应用设置
  Future<Map<String, dynamic>?> getAppSettings() async {
    try {
      final settingsStr = _storage[AppConstants.settingsKey];
      if (settingsStr != null) {
        return jsonDecode(settingsStr) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('获取应用设置失败: $e');
      return null;
    }
  }

  /// 清除应用设置
  Future<void> clearAppSettings() async {
    try {
      _storage.remove(AppConstants.settingsKey);
      debugPrint('应用设置已清除');
    } catch (e) {
      debugPrint('清除应用设置失败: $e');
    }
  }

  /// 清除所有数据
  Future<void> clearAll() async {
    try {
      _storage.clear();
      debugPrint('所有数据已清除');
    } catch (e) {
      debugPrint('清除所有数据失败: $e');
    }
  }
}
