import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../constants/app_constants.dart';
import '../utils/app_router.dart';

/// 主页面
class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        actions: [IconButton(icon: const Icon(Icons.settings), onPressed: () => context.goSettings())],
      ),
      body: const Padding(
        padding: EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('欢迎使用 SIP Server Client', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: AppConstants.defaultPadding),
            Text('这是一个基于Flutter的SIP服务器客户端应用程序。', style: TextStyle(fontSize: 16)),
            SizedBox(height: AppConstants.defaultPadding * 2),
            // TODO: 添加更多功能组件
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: 添加主要操作
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
