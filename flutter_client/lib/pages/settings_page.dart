import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';

/// 设置页面
class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // 应用设置部分
          _buildSectionHeader('应用设置'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.palette),
                  title: const Text('主题设置'),
                  subtitle: const Text('浅色主题'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // TODO: 实现主题切换
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.language),
                  title: const Text('语言设置'),
                  subtitle: const Text('简体中文'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // TODO: 实现语言切换
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.notifications),
                  title: const Text('通知设置'),
                  trailing: Switch(
                    value: true,
                    onChanged: (value) {
                      // TODO: 实现通知开关
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // 服务器设置部分
          _buildSectionHeader('服务器设置'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.dns),
                  title: const Text('服务器地址'),
                  subtitle: const Text('localhost:8080'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // TODO: 实现服务器地址设置
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.timer),
                  title: const Text('连接超时'),
                  subtitle: const Text('30秒'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // TODO: 实现超时设置
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // 关于部分
          _buildSectionHeader('关于'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('版本信息'),
                  subtitle: const Text(AppConstants.appVersion),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.help),
                  title: const Text('帮助与支持'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // TODO: 打开帮助页面
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.privacy_tip),
                  title: const Text('隐私政策'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // TODO: 打开隐私政策
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppConstants.defaultPadding / 2,
        bottom: AppConstants.defaultPadding / 2,
        top: AppConstants.defaultPadding,
      ),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.grey,
        ),
      ),
    );
  }
}
